{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/homepage-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HomePageClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePageClient() from the server but HomePageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/homepage-client.tsx <module evaluation>\",\n    \"HomePageClient\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/homepage-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HomePageClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePageClient() from the server but HomePageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/homepage-client.tsx\",\n    \"HomePageClient\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gDACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/skeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeaturedPostSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeaturedPostSkeleton() from the server but FeaturedPostSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"FeaturedPostSkeleton\",\n);\nexport const FilterSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FilterSkeleton() from the server but FilterSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"FilterSkeleton\",\n);\nexport const HeroSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Hero<PERSON><PERSON>eton() from the server but Hero<PERSON>kel<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"HeroSkeleton\",\n);\nexport const PaginationSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call PaginationSkeleton() from the server but PaginationSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"PaginationSkeleton\",\n);\nexport const PostCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostCardSkeleton() from the server but PostCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"PostCardSkeleton\",\n);\nexport const PostGridSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostGridSkeleton() from the server but PostGridSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"PostGridSkeleton\",\n);\nexport const SearchBarSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SearchBarSkeleton() from the server but SearchBarSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"SearchBarSkeleton\",\n);\nexport const Skeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx <module evaluation>\",\n    \"Skeleton\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,6DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,6DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6DACA", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/skeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeaturedPostSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeaturedPostSkeleton() from the server but FeaturedPostSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"FeaturedPostSkeleton\",\n);\nexport const FilterSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FilterSkeleton() from the server but FilterSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"FilterSkeleton\",\n);\nexport const HeroSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeroSkeleton() from the server but <PERSON><PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"HeroSkeleton\",\n);\nexport const PaginationSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call PaginationSkeleton() from the server but PaginationSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"PaginationSkeleton\",\n);\nexport const PostCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostCardSkeleton() from the server but PostCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"PostCardSkeleton\",\n);\nexport const PostGridSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostGridSkeleton() from the server but PostGridSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"PostGridSkeleton\",\n);\nexport const SearchBarSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SearchBarSkeleton() from the server but SearchBarSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"SearchBarSkeleton\",\n);\nexport const Skeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/skeleton.tsx\",\n    \"Skeleton\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,yCACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yCACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,yCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yCACA", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { createClient } from '@/lib/supabase/server'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { Post } from '@/types/database'\nimport { HomePageClient } from '@/components/homepage-client'\nimport { HeroSkeleton, PostGridSkeleton } from '@/components/skeleton'\n\nasync function getPosts(): Promise<Post[]> {\n  const supabase = await createClient()\n  const { data: posts, error } = await supabase\n    .from('posts')\n    .select('*')\n    .order('created_at', { ascending: false })\n\n  if (error) {\n    console.error('Error fetching posts:', error)\n    return []\n  }\n\n  console.log('Fetched posts count:', posts?.length || 0)\n  return posts || []\n}\n\nexport default async function Home() {\n  const posts = await getPosts()\n  const user = await getCurrentUser()\n  const userIsAdmin = user ? await isAdmin(user.id) : false\n\n  return (\n    <Suspense fallback={\n      <div className=\"min-h-screen bg-background\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <HeroSkeleton />\n          <PostGridSkeleton />\n        </div>\n      </div>\n    }>\n      <HomePageClient\n        posts={posts}\n        user={user}\n        userIsAdmin={userIsAdmin}\n      />\n    </Suspense>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;IAEA,QAAQ,GAAG,CAAC,wBAAwB,OAAO,UAAU;IACrD,OAAO,SAAS,EAAE;AACpB;AAEe,eAAe;IAC5B,MAAM,QAAQ,MAAM;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,cAAc,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE,IAAI;IAEpD,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBACR,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8HAAA,CAAA,eAAY;;;;;kCACb,8OAAC,8HAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;kBAIrB,cAAA,8OAAC,wIAAA,CAAA,iBAAc;YACb,OAAO;YACP,MAAM;YACN,aAAa;;;;;;;;;;;AAIrB", "debugId": null}}]}