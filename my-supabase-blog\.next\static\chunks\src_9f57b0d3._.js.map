{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Calculate estimated reading time for a text\n * @param text - The text content to analyze\n * @param wordsPerMinute - Average reading speed (default: 200 words per minute)\n * @returns Estimated reading time in minutes\n */\nexport function calculateReadingTime(text: string, wordsPerMinute: number = 200): number {\n  // Remove markdown syntax and HTML tags for more accurate word count\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  const wordCount = cleanText.split(' ').filter(word => word.length > 0).length\n  const readingTime = Math.ceil(wordCount / wordsPerMinute)\n  \n  return Math.max(1, readingTime) // Minimum 1 minute\n}\n\n/**\n * Create an excerpt from text content\n * @param text - The full text content\n * @param maxLength - Maximum length of the excerpt (default: 200)\n * @returns Truncated excerpt with ellipsis if needed\n */\nexport function createExcerpt(text: string, maxLength: number = 200): string {\n  // Remove markdown syntax for cleaner excerpt\n  const cleanText = text\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/`[^`]*`/g, '') // Remove inline code\n    .replace(/#{1,6}\\s/g, '') // Remove markdown headers\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1') // Remove bold markdown\n    .replace(/\\*([^*]+)\\*/g, '$1') // Remove italic markdown\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/\\s+/g, ' ') // Normalize whitespace\n    .trim()\n\n  if (cleanText.length <= maxLength) {\n    return cleanText\n  }\n\n  // Find the last complete word within the limit\n  const truncated = cleanText.substring(0, maxLength)\n  const lastSpaceIndex = truncated.lastIndexOf(' ')\n  \n  if (lastSpaceIndex > 0) {\n    return truncated.substring(0, lastSpaceIndex) + '...'\n  }\n  \n  return truncated + '...'\n}\n\n/**\n * Search through posts by title and content\n * @param posts - Array of posts to search through\n * @param query - Search query string\n * @returns Filtered array of posts matching the query\n */\nexport function searchPosts<T extends { title: string; content: string }>(\n  posts: T[],\n  query: string\n): T[] {\n  if (!query.trim()) {\n    return posts\n  }\n\n  const searchTerm = query.toLowerCase().trim()\n  \n  return posts.filter(post => {\n    const titleMatch = post.title.toLowerCase().includes(searchTerm)\n    const contentMatch = post.content.toLowerCase().includes(searchTerm)\n    return titleMatch || contentMatch\n  })\n}\n\n/**\n * Paginate an array of items\n * @param items - Array of items to paginate\n * @param page - Current page number (1-based)\n * @param itemsPerPage - Number of items per page\n * @returns Object with paginated items and pagination info\n */\nexport function paginateItems<T>(\n  items: T[],\n  page: number,\n  itemsPerPage: number\n): {\n  items: T[]\n  totalItems: number\n  totalPages: number\n  currentPage: number\n  hasNextPage: boolean\n  hasPreviousPage: boolean\n} {\n  const totalItems = items.length\n  const totalPages = Math.ceil(totalItems / itemsPerPage)\n  const currentPage = Math.max(1, Math.min(page, totalPages))\n  const startIndex = (currentPage - 1) * itemsPerPage\n  const endIndex = startIndex + itemsPerPage\n  \n  return {\n    items: items.slice(startIndex, endIndex),\n    totalItems,\n    totalPages,\n    currentPage,\n    hasNextPage: currentPage < totalPages,\n    hasPreviousPage: currentPage > 1\n  }\n}\n\n/**\n * Format a date for display\n * @param date - Date string or Date object\n * @param options - Intl.DateTimeFormat options\n * @returns Formatted date string\n */\nexport function formatDate(\n  date: string | Date,\n  options: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  }\n): string {\n  return new Date(date).toLocaleDateString('en-US', options)\n}\n\n/**\n * Get relative time string (e.g., \"2 days ago\")\n * @param date - Date string or Date object\n * @returns Relative time string\n */\nexport function getRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return 'just now'\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,qBAAqB,IAAY,EAAE,iBAAyB,GAAG;IAC7E,oEAAoE;IACpE,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,MAAM,YAAY,UAAU,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAC7E,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY;IAE1C,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa,mBAAmB;;AACrD;AAQO,SAAS,cAAc,IAAY,EAAE,YAAoB,GAAG;IACjE,6CAA6C;IAC7C,MAAM,YAAY,KACf,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,YAAY,IAAI,qBAAqB;KAC7C,OAAO,CAAC,aAAa,IAAI,0BAA0B;KACnD,OAAO,CAAC,oBAAoB,MAAM,uBAAuB;KACzD,OAAO,CAAC,gBAAgB,MAAM,yBAAyB;KACvD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,YAAY,IAAI,mBAAmB;KAC3C,OAAO,CAAC,QAAQ,KAAK,uBAAuB;KAC5C,IAAI;IAEP,IAAI,UAAU,MAAM,IAAI,WAAW;QACjC,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,YAAY,UAAU,SAAS,CAAC,GAAG;IACzC,MAAM,iBAAiB,UAAU,WAAW,CAAC;IAE7C,IAAI,iBAAiB,GAAG;QACtB,OAAO,UAAU,SAAS,CAAC,GAAG,kBAAkB;IAClD;IAEA,OAAO,YAAY;AACrB;AAQO,SAAS,YACd,KAAU,EACV,KAAa;IAEb,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,MAAM,MAAM,CAAC,CAAA;QAClB,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QACrD,MAAM,eAAe,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;QACzD,OAAO,cAAc;IACvB;AACF;AASO,SAAS,cACd,KAAU,EACV,IAAY,EACZ,YAAoB;IASpB,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAC1C,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;IAC/C,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAE9B,OAAO;QACL,OAAO,MAAM,KAAK,CAAC,YAAY;QAC/B;QACA;QACA;QACA,aAAa,cAAc;QAC3B,iBAAiB,cAAc;IACjC;AACF;AAQO,SAAS,WACd,IAAmB,EACnB,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;AACpD;AAOO,SAAS,gBAAgB,IAAmB;IACjD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,kBAAkB,IAAI,KAAK,IAAI,IAAI,CAAC;IACvE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC;IAC9D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,MAAM,EAAE,iBAAiB,IAAI,KAAK,IAAI,IAAI,CAAC;IACpE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;AACjE", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-card.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Post } from '@/types/database'\nimport { calculateReadingTime, createExcerpt, formatDate, getRelativeTime, cn } from '@/lib/utils'\n\ninterface PostCardProps {\n  post: Post\n  featured?: boolean\n  className?: string\n  showExcerpt?: boolean\n  excerptLength?: number\n  style?: React.CSSProperties\n}\n\nexport function PostCard({ \n  post, \n  featured = false, \n  className,\n  showExcerpt = true,\n  excerptLength = 200\n}: PostCardProps) {\n  const readingTime = calculateReadingTime(post.content)\n  const excerpt = showExcerpt ? createExcerpt(post.content, excerptLength) : ''\n  const isRecent = new Date(post.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Within last 7 days\n\n  if (featured) {\n    return (\n      <article className={cn(\n        \"group relative overflow-hidden\",\n        \"bg-gradient-to-br from-primary/5 via-primary/3 to-background\",\n        \"rounded-2xl border border-primary/20 shadow-lg\",\n        \"hover:shadow-xl hover:border-primary/30\",\n        \"transition-all duration-300\",\n        \"p-6 lg:p-8\",\n        className\n      )}>\n        {/* Featured Badge */}\n        <div className=\"absolute top-4 right-4\" suppressHydrationWarning>\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary text-primary-foreground shadow-sm\">\n            <svg className=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n            Featured\n          </span>\n        </div>\n\n        <div className=\"space-y-4\" suppressHydrationWarning>\n          <div suppressHydrationWarning>\n            <h2 className=\"text-2xl lg:text-3xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors leading-tight\">\n              <Link href={`/posts/${post.id}`} className=\"block\">\n                {post.title}\n              </Link>\n            </h2>\n            \n            {showExcerpt && (\n              <p className=\"text-muted-foreground leading-relaxed text-base lg:text-lg\">\n                {excerpt}\n              </p>\n            )}\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2\" suppressHydrationWarning>\n            <div className=\"flex items-center gap-4 text-sm text-muted-foreground\" suppressHydrationWarning>\n              <time className=\"flex items-center gap-1\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                {formatDate(post.created_at)}\n              </time>\n              \n              <span className=\"flex items-center gap-1\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                {readingTime} min read\n              </span>\n            </div>\n\n            <Link\n              href={`/posts/${post.id}`}\n              className=\"inline-flex items-center text-primary hover:text-primary/80 font-medium transition-all duration-200 group/link text-sm\"\n            >\n              Read full article\n              <svg className=\"w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </article>\n    )\n  }\n\n  return (\n    <article className={cn(\n      \"group relative overflow-hidden\",\n      \"bg-card rounded-xl shadow-sm border border-border\",\n      \"hover:shadow-xl hover:border-primary/30 hover:-translate-y-1\",\n      \"transition-all duration-300 ease-out\",\n      \"p-6\",\n      className\n    )}>\n      {/* New Post Indicator */}\n      {isRecent && (\n        <div className=\"absolute top-4 right-4 z-10\" suppressHydrationWarning>\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg\">\n            <svg className=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            New\n          </span>\n        </div>\n      )}\n\n      {/* Gradient overlay on hover */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\" suppressHydrationWarning />\n\n      <div className=\"relative space-y-4\" suppressHydrationWarning>\n        <div suppressHydrationWarning>\n          <h3 className=\"text-xl font-semibold text-card-foreground mb-3 group-hover:text-primary transition-colors leading-tight line-clamp-2\">\n            <Link href={`/posts/${post.id}`} className=\"block\">\n              {post.title}\n            </Link>\n          </h3>\n\n          {showExcerpt && (\n            <p className=\"text-muted-foreground leading-relaxed line-clamp-3 text-sm\">\n              {excerpt}\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-4 border-t border-border/50\" suppressHydrationWarning>\n          <div className=\"flex items-center gap-4\" suppressHydrationWarning>\n            <time className=\"flex items-center gap-2 text-sm text-muted-foreground\" title={formatDate(post.created_at)}>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n              {getRelativeTime(post.created_at)}\n            </time>\n\n            <span className=\"inline-flex items-center gap-1 px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium\">\n              <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              {readingTime}\n            </span>\n          </div>\n\n          <Link\n            href={`/posts/${post.id}`}\n            className=\"group inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-all duration-200\"\n          >\n            Read more\n            <svg className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </article>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAWO,SAAS,SAAS,EACvB,IAAI,EACJ,WAAW,KAAK,EAChB,SAAS,EACT,cAAc,IAAI,EAClB,gBAAgB,GAAG,EACL;IACd,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,OAAO;IACrD,MAAM,UAAU,cAAc,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,OAAO,EAAE,iBAAiB;IAC3E,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,qBAAqB;;IAEjH,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAQ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,kCACA,gEACA,kDACA,2CACA,+BACA,cACA;;8BAGA,6LAAC;oBAAI,WAAU;oBAAyB,wBAAwB;8BAC9D,cAAA,6LAAC;wBAAK,WAAU;;0CACd,6LAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;oBAAY,wBAAwB;;sCACjD,6LAAC;4BAAI,wBAAwB;;8CAC3B,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wCAAE,WAAU;kDACxC,KAAK,KAAK;;;;;;;;;;;gCAId,6BACC,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;4BAA0E,wBAAwB;;8CAC/G,6LAAC;oCAAI,WAAU;oCAAwD,wBAAwB;;sDAC7F,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;sDAG7B,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDAEtE;gDAAY;;;;;;;;;;;;;8CAIjB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oCACzB,WAAU;;wCACX;sDAEC,6LAAC;4CAAI,WAAU;4CAAmE,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC1H,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnF;IAEA,qBACE,6LAAC;QAAQ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,kCACA,qDACA,gEACA,wCACA,OACA;;YAGC,0BACC,6LAAC;gBAAI,WAAU;gBAA8B,wBAAwB;0BACnE,cAAA,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;4BAAe,MAAK;4BAAe,SAAQ;sCACxD,cAAA,6LAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAwI,UAAS;;;;;;;;;;;wBACxK;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAU;gBAAgJ,wBAAwB;;;;;;0BAEvL,6LAAC;gBAAI,WAAU;gBAAqB,wBAAwB;;kCAC1D,6LAAC;wBAAI,wBAAwB;;0CAC3B,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oCAAE,WAAU;8CACxC,KAAK,KAAK;;;;;;;;;;;4BAId,6BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAKP,6LAAC;wBAAI,WAAU;wBAAoG,wBAAwB;;0CACzI,6LAAC;gCAAI,WAAU;gCAA0B,wBAAwB;;kDAC/D,6LAAC;wCAAK,WAAU;wCAAwD,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;0DACvG,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU;;;;;;;kDAGlC,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE;;;;;;;;;;;;;0CAIL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gCACzB,WAAU;;oCACX;kDAEC,6LAAC;wCAAI,WAAU;wCAAsE,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7H,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;KApJgB", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/search-bar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void\n  placeholder?: string\n  className?: string\n  autoFocus?: boolean\n}\n\nexport function SearchBar({ \n  onSearch, \n  placeholder = \"Search posts...\", \n  className,\n  autoFocus = false \n}: SearchBarProps) {\n  const [query, setQuery] = useState('')\n  const [isFocused, setIsFocused] = useState(false)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  useEffect(() => {\n    if (autoFocus && inputRef.current) {\n      inputRef.current.focus()\n    }\n  }, [autoFocus])\n\n  useEffect(() => {\n    // Debounce search to avoid too many calls\n    const timeoutId = setTimeout(() => {\n      onSearch(query)\n    }, 300)\n\n    return () => clearTimeout(timeoutId)\n  }, [query, onSearch])\n\n  const handleClear = () => {\n    setQuery('')\n    if (inputRef.current) {\n      inputRef.current.focus()\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClear()\n    }\n  }\n\n  return (\n    <div className={cn(\n      \"relative group\",\n      className\n    )} suppressHydrationWarning>\n      <div className={cn(\n        \"relative flex items-center transition-all duration-200\",\n        \"bg-background border border-border rounded-lg\",\n        \"hover:border-primary/30 focus-within:border-primary/50\",\n        \"shadow-sm hover:shadow-md focus-within:shadow-md\",\n        isFocused && \"ring-2 ring-primary/20\"\n      )} suppressHydrationWarning>\n        {/* Search Icon */}\n        <div className=\"absolute left-3 flex items-center pointer-events-none\" suppressHydrationWarning>\n          <svg \n            className={cn(\n              \"w-4 h-4 transition-colors duration-200\",\n              isFocused || query ? \"text-primary\" : \"text-muted-foreground\"\n            )} \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" \n            />\n          </svg>\n        </div>\n\n        {/* Input Field */}\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => setIsFocused(false)}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          className={cn(\n            \"w-full pl-10 pr-10 py-2.5 sm:py-3\",\n            \"bg-transparent border-0 outline-none\",\n            \"text-foreground placeholder:text-muted-foreground\",\n            \"text-sm sm:text-base\"\n          )}\n        />\n\n        {/* Clear Button */}\n        {query && (\n          <button\n            onClick={handleClear}\n            className={cn(\n              \"absolute right-3 flex items-center justify-center\",\n              \"w-5 h-5 rounded-full\",\n              \"text-muted-foreground hover:text-foreground\",\n              \"hover:bg-muted transition-all duration-200\",\n              \"focus:outline-none focus:ring-2 focus:ring-primary/20\"\n            )}\n            aria-label=\"Clear search\"\n          >\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Search Results Indicator */}\n      {query && (\n        <div className=\"absolute top-full left-0 right-0 mt-1\">\n          <div className=\"text-xs text-muted-foreground px-3 py-1\">\n            Searching for \"{query}\"...\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\ninterface SearchResultsProps {\n  query: string\n  totalResults: number\n  className?: string\n}\n\nexport function SearchResults({ query, totalResults, className }: SearchResultsProps) {\n  if (!query) return null\n\n  return (\n    <div className={cn(\n      \"flex items-center justify-between\",\n      \"px-4 py-2 mb-6\",\n      \"bg-muted/50 rounded-lg border border-border\",\n      className\n    )}>\n      <div className=\"flex items-center gap-2\">\n        <svg className=\"w-4 h-4 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n        </svg>\n        <span className=\"text-sm text-foreground\">\n          {totalResults === 0 ? (\n            <>No results found for <strong>\"{query}\"</strong></>\n          ) : (\n            <>\n              {totalResults} result{totalResults === 1 ? '' : 's'} for <strong>\"{query}\"</strong>\n            </>\n          )}\n        </span>\n      </div>\n      \n      {totalResults === 0 && (\n        <div className=\"text-xs text-muted-foreground\">\n          Try different keywords\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,iBAAiB,EAC/B,SAAS,EACT,YAAY,KAAK,EACF;;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,aAAa,SAAS,OAAO,EAAE;gBACjC,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;8BAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,0CAA0C;YAC1C,MAAM,YAAY;iDAAW;oBAC3B,SAAS;gBACX;gDAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAO;KAAS;IAEpB,MAAM,cAAc;QAClB,SAAS;QACT,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kBACA;QACC,wBAAwB;;0BACzB,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,0DACA,iDACA,0DACA,oDACA,aAAa;gBACZ,wBAAwB;;kCAEzB,6LAAC;wBAAI,WAAU;wBAAwD,wBAAwB;kCAC7F,cAAA,6LAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,aAAa,QAAQ,iBAAiB;4BAExC,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAMR,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,aAAa;wBAC5B,QAAQ,IAAM,aAAa;wBAC3B,WAAW;wBACX,aAAa;wBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,wCACA,qDACA;;;;;;oBAKH,uBACC,6LAAC;wBACC,SAAS;wBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,wBACA,+CACA,8CACA;wBAEF,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;YAO5E,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAA0C;wBACvC;wBAAM;;;;;;;;;;;;;;;;;;AAMlC;GAtHgB;KAAA;AA8HT,SAAS,cAAc,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAsB;IAClF,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qCACA,kBACA,+CACA;;0BAEA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAgC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACvF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAK,WAAU;kCACb,iBAAiB,kBAChB;;gCAAE;8CAAqB,6LAAC;;wCAAO;wCAAE;wCAAM;;;;;;;;yDAEvC;;gCACG;gCAAa;gCAAQ,iBAAiB,IAAI,KAAK;gCAAI;8CAAK,6LAAC;;wCAAO;wCAAE;wCAAM;;;;;;;;;;;;;;;;;;;;YAMhF,iBAAiB,mBAChB,6LAAC;gBAAI,WAAU;0BAAgC;;;;;;;;;;;;AAMvD;MAhCgB", "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/pagination.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface PaginationProps {\n  currentPage: number\n  totalPages: number\n  onPageChange: (page: number) => void\n  className?: string\n  showFirstLast?: boolean\n  maxVisiblePages?: number\n}\n\nexport function Pagination({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className,\n  showFirstLast = true,\n  maxVisiblePages = 5\n}: PaginationProps) {\n  if (totalPages <= 1) return null\n\n  const getVisiblePages = () => {\n    const pages: (number | string)[] = []\n    \n    if (totalPages <= maxVisiblePages) {\n      // Show all pages if total is less than max visible\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i)\n      }\n    } else {\n      // Calculate start and end of visible range\n      let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))\n      let end = Math.min(totalPages, start + maxVisiblePages - 1)\n      \n      // Adjust start if we're near the end\n      if (end - start + 1 < maxVisiblePages) {\n        start = Math.max(1, end - maxVisiblePages + 1)\n      }\n      \n      // Add first page and ellipsis if needed\n      if (start > 1) {\n        pages.push(1)\n        if (start > 2) {\n          pages.push('...')\n        }\n      }\n      \n      // Add visible pages\n      for (let i = start; i <= end; i++) {\n        pages.push(i)\n      }\n      \n      // Add ellipsis and last page if needed\n      if (end < totalPages) {\n        if (end < totalPages - 1) {\n          pages.push('...')\n        }\n        pages.push(totalPages)\n      }\n    }\n    \n    return pages\n  }\n\n  const visiblePages = getVisiblePages()\n\n  const buttonClass = (isActive: boolean = false, isDisabled: boolean = false) =>\n    cn(\n      \"flex items-center justify-center\",\n      \"min-w-[40px] h-10 px-3\",\n      \"text-sm font-medium\",\n      \"border border-border rounded-lg\",\n      \"transition-all duration-200\",\n      \"focus:outline-none focus:ring-2 focus:ring-primary/20\",\n      isActive\n        ? \"bg-primary text-primary-foreground border-primary shadow-sm\"\n        : isDisabled\n        ? \"bg-muted text-muted-foreground cursor-not-allowed\"\n        : \"bg-background text-foreground hover:bg-muted hover:border-primary/30 hover:shadow-sm\"\n    )\n\n  return (\n    <nav \n      className={cn(\"flex items-center justify-center gap-1 sm:gap-2\", className)}\n      aria-label=\"Pagination\"\n    >\n      {/* First Page Button */}\n      {showFirstLast && currentPage > 1 && (\n        <button\n          onClick={() => onPageChange(1)}\n          className={buttonClass(false, false)}\n          aria-label=\"Go to first page\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 19l-7-7 7-7m8 14l-7-7 7-7\" />\n          </svg>\n        </button>\n      )}\n\n      {/* Previous Page Button */}\n      <button\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage <= 1}\n        className={buttonClass(false, currentPage <= 1)}\n        aria-label=\"Go to previous page\"\n      >\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n        </svg>\n        <span className=\"hidden sm:inline ml-1\">Previous</span>\n      </button>\n\n      {/* Page Numbers */}\n      <div className=\"flex items-center gap-1\">\n        {visiblePages.map((page, index) => (\n          <div key={index}>\n            {page === '...' ? (\n              <span className=\"flex items-center justify-center min-w-[40px] h-10 text-muted-foreground\">\n                ...\n              </span>\n            ) : (\n              <button\n                onClick={() => onPageChange(page as number)}\n                className={buttonClass(page === currentPage, false)}\n                aria-label={`Go to page ${page}`}\n                aria-current={page === currentPage ? 'page' : undefined}\n              >\n                {page}\n              </button>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Next Page Button */}\n      <button\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage >= totalPages}\n        className={buttonClass(false, currentPage >= totalPages)}\n        aria-label=\"Go to next page\"\n      >\n        <span className=\"hidden sm:inline mr-1\">Next</span>\n        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n        </svg>\n      </button>\n\n      {/* Last Page Button */}\n      {showFirstLast && currentPage < totalPages && (\n        <button\n          onClick={() => onPageChange(totalPages)}\n          className={buttonClass(false, false)}\n          aria-label=\"Go to last page\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 5l7 7-7 7M5 5l7 7-7 7\" />\n          </svg>\n        </button>\n      )}\n    </nav>\n  )\n}\n\ninterface PaginationInfoProps {\n  currentPage: number\n  totalPages: number\n  totalItems: number\n  itemsPerPage: number\n  className?: string\n}\n\nexport function PaginationInfo({\n  currentPage,\n  totalPages,\n  totalItems,\n  itemsPerPage,\n  className\n}: PaginationInfoProps) {\n  const startItem = (currentPage - 1) * itemsPerPage + 1\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems)\n\n  return (\n    <div className={cn(\n      \"flex items-center justify-center text-sm text-muted-foreground\",\n      className\n    )}>\n      Showing {startItem} to {endItem} of {totalItems} posts\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaO,SAAS,WAAW,EACzB,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACT,gBAAgB,IAAI,EACpB,kBAAkB,CAAC,EACH;IAChB,IAAI,cAAc,GAAG,OAAO;IAE5B,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,IAAI,cAAc,iBAAiB;YACjC,mDAAmD;YACnD,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;QACF,OAAO;YACL,2CAA2C;YAC3C,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,kBAAkB;YACnE,IAAI,MAAM,KAAK,GAAG,CAAC,YAAY,QAAQ,kBAAkB;YAEzD,qCAAqC;YACrC,IAAI,MAAM,QAAQ,IAAI,iBAAiB;gBACrC,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,kBAAkB;YAC9C;YAEA,wCAAwC;YACxC,IAAI,QAAQ,GAAG;gBACb,MAAM,IAAI,CAAC;gBACX,IAAI,QAAQ,GAAG;oBACb,MAAM,IAAI,CAAC;gBACb;YACF;YAEA,oBAAoB;YACpB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,MAAM,IAAI,CAAC;YACb;YAEA,uCAAuC;YACvC,IAAI,MAAM,YAAY;gBACpB,IAAI,MAAM,aAAa,GAAG;oBACxB,MAAM,IAAI,CAAC;gBACb;gBACA,MAAM,IAAI,CAAC;YACb;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,cAAc,CAAC,WAAoB,KAAK,EAAE,aAAsB,KAAK,GACzE,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACC,oCACA,0BACA,uBACA,mCACA,+BACA,yDACA,WACI,gEACA,aACA,sDACA;IAGR,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;QACjE,cAAW;;YAGV,iBAAiB,cAAc,mBAC9B,6LAAC;gBACC,SAAS,IAAM,aAAa;gBAC5B,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBACC,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;kCACE,SAAS,sBACR,6LAAC;4BAAK,WAAU;sCAA2E;;;;;iDAI3F,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,YAAY,SAAS,aAAa;4BAC7C,cAAY,CAAC,WAAW,EAAE,MAAM;4BAChC,gBAAc,SAAS,cAAc,SAAS;sCAE7C;;;;;;uBAZG;;;;;;;;;;0BAoBd,6LAAC;gBACC,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;gBACzB,WAAW,YAAY,OAAO,eAAe;gBAC7C,cAAW;;kCAEX,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAKxE,iBAAiB,cAAc,4BAC9B,6LAAC;gBACC,SAAS,IAAM,aAAa;gBAC5B,WAAW,YAAY,OAAO;gBAC9B,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMjF;KAtJgB;AAgKT,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACW;IACpB,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,eAAe;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kEACA;;YACC;YACQ;YAAU;YAAK;YAAQ;YAAK;YAAW;;;;;;;AAGtD;MAlBgB", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/category-filter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect, useRef } from 'react'\nimport { Post } from '@/types/database'\nimport { cn } from '@/lib/utils'\n\ninterface CategoryFilterProps {\n  posts: Post[]\n  onFilterChange: (filteredPosts: Post[]) => void\n  className?: string\n}\n\n// Define categories based on common keywords\nconst CATEGORIES = [\n  {\n    id: 'all',\n    name: 'All Posts',\n    keywords: [],\n    icon: '📝'\n  },\n  {\n    id: 'technology',\n    name: 'Technology',\n    keywords: ['tech', 'technology', 'programming', 'code', 'software', 'development', 'web', 'app', 'javascript', 'react', 'next', 'node', 'python', 'ai', 'machine learning', 'data', 'api', 'database'],\n    icon: '💻'\n  },\n  {\n    id: 'design',\n    name: 'Design',\n    keywords: ['design', 'ui', 'ux', 'interface', 'user experience', 'visual', 'graphic', 'layout', 'typography', 'color', 'branding', 'figma', 'sketch'],\n    icon: '🎨'\n  },\n  {\n    id: 'business',\n    name: 'Business',\n    keywords: ['business', 'startup', 'entrepreneur', 'marketing', 'strategy', 'growth', 'revenue', 'sales', 'management', 'leadership', 'productivity', 'finance'],\n    icon: '💼'\n  },\n  {\n    id: 'lifestyle',\n    name: 'Lifestyle',\n    keywords: ['lifestyle', 'health', 'fitness', 'travel', 'food', 'cooking', 'wellness', 'mindfulness', 'hobby', 'personal', 'life', 'tips', 'advice'],\n    icon: '🌟'\n  },\n  {\n    id: 'tutorial',\n    name: 'Tutorials',\n    keywords: ['tutorial', 'guide', 'how to', 'step by step', 'learn', 'beginner', 'introduction', 'getting started', 'walkthrough', 'example'],\n    icon: '📚'\n  },\n  {\n    id: 'news',\n    name: 'News & Updates',\n    keywords: ['news', 'update', 'announcement', 'release', 'new', 'latest', 'breaking', 'trending', 'current', 'recent'],\n    icon: '📰'\n  }\n]\n\nfunction categorizePost(post: Post): string[] {\n  const content = (post.title + ' ' + post.content).toLowerCase()\n  const categories: string[] = []\n\n  for (const category of CATEGORIES) {\n    if (category.id === 'all') continue\n    \n    const hasKeyword = category.keywords.some(keyword => \n      content.includes(keyword.toLowerCase())\n    )\n    \n    if (hasKeyword) {\n      categories.push(category.id)\n    }\n  }\n\n  // If no categories found, categorize as 'other'\n  return categories.length > 0 ? categories : ['other']\n}\n\nexport function CategoryFilter({ posts, onFilterChange, className }: CategoryFilterProps) {\n  const [selectedCategory, setSelectedCategory] = useState('all')\n  const isInitialMount = useRef(true)\n\n  // Calculate post counts for each category\n  const categoryCounts = useMemo(() => {\n    const counts: Record<string, number> = { all: posts.length }\n    \n    posts.forEach(post => {\n      const postCategories = categorizePost(post)\n      postCategories.forEach(categoryId => {\n        counts[categoryId] = (counts[categoryId] || 0) + 1\n      })\n    })\n\n    return counts\n  }, [posts])\n\n  // Filter posts based on selected category\n  const filteredPosts = useMemo(() => {\n    if (selectedCategory === 'all') {\n      return posts\n    }\n\n    return posts.filter(post => {\n      const postCategories = categorizePost(post)\n      return postCategories.includes(selectedCategory)\n    })\n  }, [posts, selectedCategory])\n\n  // Update parent component when filter changes\n  useEffect(() => {\n    if (isInitialMount.current) {\n      isInitialMount.current = false\n    } else {\n      onFilterChange(filteredPosts)\n    }\n  }, [filteredPosts, onFilterChange])\n\n  const handleCategoryChange = (categoryId: string) => {\n    setSelectedCategory(categoryId)\n  }\n\n  return (\n    <div className={cn(\"space-y-4\", className)} suppressHydrationWarning>\n      {/* Category Buttons */}\n      <div className=\"flex flex-wrap gap-2\" suppressHydrationWarning>\n        {CATEGORIES.map(category => {\n          const count = categoryCounts[category.id] || 0\n          const isActive = selectedCategory === category.id\n          const isDisabled = count === 0 && category.id !== 'all'\n\n          return (\n            <button\n              key={category.id}\n              onClick={() => handleCategoryChange(category.id)}\n              disabled={isDisabled}\n              className={cn(\n                \"inline-flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200\",\n                \"border border-border hover:border-primary/30\",\n                \"focus:outline-none focus:ring-2 focus:ring-primary/20\",\n                isActive\n                  ? \"bg-primary text-primary-foreground border-primary shadow-sm\"\n                  : isDisabled\n                  ? \"bg-muted/50 text-muted-foreground cursor-not-allowed opacity-50\"\n                  : \"bg-background text-foreground hover:bg-muted hover:shadow-sm\"\n              )}\n            >\n              <span className=\"text-base\">{category.icon}</span>\n              <span>{category.name}</span>\n              {count > 0 && (\n                <span className={cn(\n                  \"px-2 py-0.5 rounded-full text-xs font-semibold\",\n                  isActive\n                    ? \"bg-primary-foreground/20 text-primary-foreground\"\n                    : \"bg-muted text-muted-foreground\"\n                )}>\n                  {count}\n                </span>\n              )}\n            </button>\n          )\n        })}\n      </div>\n\n      {/* Active Filter Info */}\n      {selectedCategory !== 'all' && (\n        <div className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg border border-border\">\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg\">\n              {CATEGORIES.find(c => c.id === selectedCategory)?.icon}\n            </span>\n            <span className=\"text-sm text-foreground\">\n              Showing <strong>{filteredPosts.length}</strong> posts in{' '}\n              <strong>{CATEGORIES.find(c => c.id === selectedCategory)?.name}</strong>\n            </span>\n          </div>\n          <button\n            onClick={() => handleCategoryChange('all')}\n            className=\"text-xs text-muted-foreground hover:text-foreground transition-colors underline\"\n          >\n            Clear filter\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Hook to get post categories\nexport function usePostCategories(posts: Post[]) {\n  return useMemo(() => {\n    const postCategories = new Map<string, string[]>()\n    \n    posts.forEach(post => {\n      const categories = categorizePost(post)\n      postCategories.set(post.id, categories)\n    })\n\n    return postCategories\n  }, [posts])\n}\n\n// Utility function to get category info\nexport function getCategoryInfo(categoryId: string) {\n  return CATEGORIES.find(c => c.id === categoryId) || null\n}\n\n// Utility function to get all available categories\nexport function getAllCategories() {\n  return CATEGORIES\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;;;AAJA;;;AAYA,6CAA6C;AAC7C,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,UAAU,EAAE;QACZ,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAC;YAAQ;YAAc;YAAe;YAAQ;YAAY;YAAe;YAAO;YAAO;YAAc;YAAS;YAAQ;YAAQ;YAAU;YAAM;YAAoB;YAAQ;YAAO;SAAW;QACtM,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAC;YAAU;YAAM;YAAM;YAAa;YAAmB;YAAU;YAAW;YAAU;YAAc;YAAS;YAAY;YAAS;SAAS;QACrJ,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAC;YAAY;YAAW;YAAgB;YAAa;YAAY;YAAU;YAAW;YAAS;YAAc;YAAc;YAAgB;SAAU;QAC/J,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAC;YAAa;YAAU;YAAW;YAAU;YAAQ;YAAW;YAAY;YAAe;YAAS;YAAY;YAAQ;YAAQ;SAAS;QACnJ,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAC;YAAY;YAAS;YAAU;YAAgB;YAAS;YAAY;YAAgB;YAAmB;YAAe;SAAU;QAC3I,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAC;YAAQ;YAAU;YAAgB;YAAW;YAAO;YAAU;YAAY;YAAY;YAAW;SAAS;QACrH,MAAM;IACR;CACD;AAED,SAAS,eAAe,IAAU;IAChC,MAAM,UAAU,CAAC,KAAK,KAAK,GAAG,MAAM,KAAK,OAAO,EAAE,WAAW;IAC7D,MAAM,aAAuB,EAAE;IAE/B,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,EAAE,KAAK,OAAO;QAE3B,MAAM,aAAa,SAAS,QAAQ,CAAC,IAAI,CAAC,CAAA,UACxC,QAAQ,QAAQ,CAAC,QAAQ,WAAW;QAGtC,IAAI,YAAY;YACd,WAAW,IAAI,CAAC,SAAS,EAAE;QAC7B;IACF;IAEA,gDAAgD;IAChD,OAAO,WAAW,MAAM,GAAG,IAAI,aAAa;QAAC;KAAQ;AACvD;AAEO,SAAS,eAAe,EAAE,KAAK,EAAE,cAAc,EAAE,SAAS,EAAuB;;IACtF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,0CAA0C;IAC1C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC7B,MAAM,SAAiC;gBAAE,KAAK,MAAM,MAAM;YAAC;YAE3D,MAAM,OAAO;0DAAC,CAAA;oBACZ,MAAM,iBAAiB,eAAe;oBACtC,eAAe,OAAO;kEAAC,CAAA;4BACrB,MAAM,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,IAAI;wBACnD;;gBACF;;YAEA,OAAO;QACT;iDAAG;QAAC;KAAM;IAEV,0CAA0C;IAC1C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,IAAI,qBAAqB,OAAO;gBAC9B,OAAO;YACT;YAEA,OAAO,MAAM,MAAM;yDAAC,CAAA;oBAClB,MAAM,iBAAiB,eAAe;oBACtC,OAAO,eAAe,QAAQ,CAAC;gBACjC;;QACF;gDAAG;QAAC;QAAO;KAAiB;IAE5B,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,GAAG;YAC3B,OAAO;gBACL,eAAe;YACjB;QACF;mCAAG;QAAC;QAAe;KAAe;IAElC,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAY,wBAAwB;;0BAElE,6LAAC;gBAAI,WAAU;gBAAuB,wBAAwB;0BAC3D,WAAW,GAAG,CAAC,CAAA;oBACd,MAAM,QAAQ,cAAc,CAAC,SAAS,EAAE,CAAC,IAAI;oBAC7C,MAAM,WAAW,qBAAqB,SAAS,EAAE;oBACjD,MAAM,aAAa,UAAU,KAAK,SAAS,EAAE,KAAK;oBAElD,qBACE,6LAAC;wBAEC,SAAS,IAAM,qBAAqB,SAAS,EAAE;wBAC/C,UAAU;wBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yGACA,gDACA,yDACA,WACI,gEACA,aACA,oEACA;;0CAGN,6LAAC;gCAAK,WAAU;0CAAa,SAAS,IAAI;;;;;;0CAC1C,6LAAC;0CAAM,SAAS,IAAI;;;;;;4BACnB,QAAQ,mBACP,6LAAC;gCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,kDACA,WACI,qDACA;0CAEH;;;;;;;uBAvBA,SAAS,EAAE;;;;;gBA4BtB;;;;;;YAID,qBAAqB,uBACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;;;;;;0CAEpD,6LAAC;gCAAK,WAAU;;oCAA0B;kDAChC,6LAAC;kDAAQ,cAAc,MAAM;;;;;;oCAAU;oCAAU;kDACzD,6LAAC;kDAAQ,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;;;;;;;;;;;;;;;;;;kCAG9D,6LAAC;wBACC,SAAS,IAAM,qBAAqB;wBACpC,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GA3GgB;KAAA;AA8GT,SAAS,kBAAkB,KAAa;;IAC7C,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qCAAE;YACb,MAAM,iBAAiB,IAAI;YAE3B,MAAM,OAAO;6CAAC,CAAA;oBACZ,MAAM,aAAa,eAAe;oBAClC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;gBAC9B;;YAEA,OAAO;QACT;oCAAG;QAAC;KAAM;AACZ;IAXgB;AAcT,SAAS,gBAAgB,UAAkB;IAChD,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe;AACtD;AAGO,SAAS;IACd,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;;8BAChB,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,6LAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GA9BgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 1569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,6LAAC,wIAAA,CAAA,cAAW;;;;;AACrB;KAFgB", "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/homepage-client.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport Link from 'next/link'\nimport { Post } from '@/types/database'\nimport { PostCard } from '@/components/post-card'\nimport { SearchBar, SearchResults } from '@/components/search-bar'\nimport { Pagination, PaginationInfo } from '@/components/pagination'\nimport { CategoryFilter } from '@/components/category-filter'\nimport { searchPosts, paginateItems } from '@/lib/utils'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\n\ninterface HomePageClientProps {\n  posts: Post[]\n  user: any\n  userIsAdmin: boolean\n}\n\nconst POSTS_PER_PAGE = 6\n\nexport function HomePageClient({ posts, user, userIsAdmin }: HomePageClientProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [currentPage, setCurrentPage] = useState(1)\n  const [filteredByCategory, setFilteredByCategory] = useState<Post[]>(posts)\n  const [isHydrated, setIsHydrated] = useState(false)\n\n  // Handle hydration\n  useEffect(() => {\n    setIsHydrated(true)\n  }, [])\n\n  // Filter posts based on search query and category\n  const filteredPosts = useMemo(() => {\n    return searchPosts(filteredByCategory, searchQuery)\n  }, [filteredByCategory, searchQuery])\n\n  // Paginate filtered posts\n  const paginatedData = useMemo(() => {\n    return paginateItems(filteredPosts, currentPage, POSTS_PER_PAGE)\n  }, [filteredPosts, currentPage])\n\n  // Reset to first page when search or category changes\n  const handleSearch = (query: string) => {\n    setSearchQuery(query)\n    setCurrentPage(1)\n  }\n\n  const handleCategoryFilter = (categoryFilteredPosts: Post[]) => {\n    setFilteredByCategory(categoryFilteredPosts)\n    setCurrentPage(1)\n  }\n\n  // Get featured post (most recent post)\n  const featuredPost = posts.length > 0 ? posts[0] : null\n\n  // Show loading state during hydration to prevent mismatch\n  if (!isHydrated) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <header className=\"border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between h-16\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-foreground\">\n                My Blog\n              </Link>\n              <ClientThemeToggle />\n            </div>\n          </div>\n        </header>\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse space-y-8\">\n            <div className=\"h-64 bg-muted rounded-lg\"></div>\n            <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n              {[...Array(6)].map((_, i) => (\n                <div key={i} className=\"h-48 bg-muted rounded-lg\"></div>\n              ))}\n            </div>\n          </div>\n        </main>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\" suppressHydrationWarning>\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6\" suppressHydrationWarning={true}>\n          <div className=\"flex justify-between items-center\" suppressHydrationWarning={true}>\n            <Link href=\"/\" className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground hover:text-primary transition-colors\">\n              My Blog\n            </Link>\n            <div className=\"flex items-center gap-1 sm:gap-2 lg:gap-4\" suppressHydrationWarning={true}>\n              <ClientThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"hidden lg:block text-sm text-muted-foreground truncate max-w-32\">\n                    Welcome, {user.email}\n                  </span>\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/new-post\"\n                      className=\"bg-primary text-primary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\n                    >\n                      <span className=\"hidden sm:inline\">New Post</span>\n                      <span className=\"sm:hidden text-lg\">+</span>\n                    </Link>\n                  )}\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/manage-posts\"\n                      className=\"bg-secondary text-secondary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\n                    >\n                      <span className=\"hidden sm:inline\">Manage</span>\n                      <span className=\"sm:hidden text-lg\">⚙️</span>\n                    </Link>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex items-center justify-center px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md touch-manipulation min-h-[44px]\"\n                    >\n                      <svg className=\"w-4 h-4 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      <span className=\"hidden sm:inline\">Sign Out</span>\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center\"\n                >\n                  Sign In\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8\">\n        {posts.length === 0 ? (\n          /* Empty State */\n          <div className=\"text-center py-16 lg:py-24\">\n            <div className=\"max-w-md mx-auto\">\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center\">\n                <svg className=\"w-8 h-8 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">No posts yet</h2>\n              <p className=\"text-muted-foreground mb-6\">Start sharing your thoughts with the world!</p>\n              {userIsAdmin && (\n                <Link\n                  href=\"/admin/new-post\"\n                  className=\"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\n                >\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  Create your first post\n                </Link>\n              )}\n            </div>\n          </div>\n        ) : (\n          <>\n            {/* Hero Section */}\n            {/* Featured Post Section */}\n            {featuredPost && (\n              <section className=\"mb-16\">\n                <div className=\"text-center mb-8\" suppressHydrationWarning={true}>\n                  <h2 className=\"text-3xl font-bold text-foreground mb-2\">Featured Post</h2>\n                  <p className=\"text-muted-foreground\">Don't miss our latest featured content</p>\n                </div>\n                <PostCard post={featuredPost} featured={true} />\n              </section>\n            )}\n\n            {/* Search and Filter Section */}\n            <section id=\"posts\" className=\"mb-8 sm:mb-12\">\n              <div className=\"flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8\" suppressHydrationWarning={true}>\n                <div className=\"text-center sm:text-left\" suppressHydrationWarning={true}>\n                  <h2 className=\"text-2xl sm:text-3xl font-bold text-foreground mb-2\">All Posts</h2>\n                  <p className=\"text-muted-foreground\">\n                    {posts.length} post{posts.length === 1 ? '' : 's'} available\n                  </p>\n                </div>\n                <div className=\"w-full sm:max-w-md sm:mx-auto lg:mx-0 lg:max-w-96\" suppressHydrationWarning={true}>\n                  <SearchBar onSearch={handleSearch} />\n                </div>\n              </div>\n\n              {/* Category Filter */}\n              <div className=\"mb-6 sm:mb-8\" suppressHydrationWarning={true}>\n                <CategoryFilter\n                  posts={posts}\n                  onFilterChange={handleCategoryFilter}\n                />\n              </div>\n\n              {/* Search Results Info */}\n              <SearchResults query={searchQuery} totalResults={filteredPosts.length} />\n            </section>\n\n            {/* Posts Grid */}\n            <section className=\"mb-16\">\n              {paginatedData.items.length === 0 ? (\n                <div className=\"text-center py-20\">\n                  <div className=\"w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-muted to-muted/50 rounded-2xl flex items-center justify-center\">\n                    <svg className=\"w-10 h-10 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-2xl font-semibold text-foreground mb-3\">No posts found</h3>\n                  <p className=\"text-muted-foreground text-lg mb-6\">Try adjusting your search terms or browse all posts</p>\n                  <Link\n                    href=\"#posts\"\n                    onClick={() => {\n                      setSearchQuery('')\n                      setCurrentPage(1)\n                    }}\n                    className=\"inline-flex items-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors\"\n                  >\n                    Clear Search\n                  </Link>\n                </div>\n              ) : (\n                <div className=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3\" suppressHydrationWarning={true}>\n                  {paginatedData.items.map((post, index) => (\n                    <PostCard\n                      key={post.id}\n                      post={post}\n                      className=\"animate-fade-in hover:scale-[1.02] transition-transform duration-300\"\n                      style={{\n                        animationDelay: `${index * 0.1}s`,\n                        animationFillMode: 'both'\n                      } as React.CSSProperties}\n                    />\n                  ))}\n                </div>\n              )}\n            </section>\n\n            {/* Pagination */}\n            {paginatedData.totalPages > 1 && (\n              <section className=\"mb-12 space-y-6\">\n                <Pagination\n                  currentPage={paginatedData.currentPage}\n                  totalPages={paginatedData.totalPages}\n                  onPageChange={setCurrentPage}\n                />\n                <PaginationInfo\n                  currentPage={paginatedData.currentPage}\n                  totalPages={paginatedData.totalPages}\n                  totalItems={paginatedData.totalItems}\n                  itemsPerPage={POSTS_PER_PAGE}\n                />\n              </section>\n            )}\n          </>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAkBA,MAAM,iBAAiB;AAEhB,SAAS,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAuB;;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,cAAc;QAChB;mCAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,OAAO,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,oBAAoB;QACzC;gDAAG;QAAC;QAAoB;KAAY;IAEpC,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,OAAO,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,aAAa;QACnD;gDAAG;QAAC;QAAe;KAAY;IAE/B,sDAAsD;IACtD,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,eAAe;IACjB;IAEA,uCAAuC;IACvC,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG;IAEnD,0DAA0D;IAC1D,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqC;;;;;;8CAG9D,6LAAC,kJAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;8BAIxB,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;uCAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxB;IAEA,qBACE,6LAAC;QAAI,WAAU;QAA6B,wBAAwB;;0BAElE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;oBAA8D,0BAA0B;8BACrG,cAAA,6LAAC;wBAAI,WAAU;wBAAoC,0BAA0B;;0CAC3E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAiG;;;;;;0CAG1H,6LAAC;gCAAI,WAAU;gCAA4C,0BAA0B;;kDACnF,6LAAC,kJAAA,CAAA,oBAAiB;;;;;oCACjB,qBACC;;0DACE,6LAAC;gDAAK,WAAU;;oDAAkE;oDACtE,KAAK,KAAK;;;;;;;4CAErB,6BACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,6BACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;0DAGxC,6LAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,6LAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;4DAAkB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACzE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;qEAKzC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAK,WAAU;0BACb,MAAM,MAAM,KAAK,IAChB,eAAe,iBACf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAgC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACvF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;4BACzC,6BACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;yCAOd;;wBAGG,8BACC,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAI,WAAU;oCAAmB,0BAA0B;;sDAC1D,6LAAC;4CAAG,WAAU;sDAA0C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC,qIAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAc,UAAU;;;;;;;;;;;;sCAK5C,6LAAC;4BAAQ,IAAG;4BAAQ,WAAU;;8CAC5B,6LAAC;oCAAI,WAAU;oCAA4C,0BAA0B;;sDACnF,6LAAC;4CAAI,WAAU;4CAA2B,0BAA0B;;8DAClE,6LAAC;oDAAG,WAAU;8DAAsD;;;;;;8DACpE,6LAAC;oDAAE,WAAU;;wDACV,MAAM,MAAM;wDAAC;wDAAM,MAAM,MAAM,KAAK,IAAI,KAAK;wDAAI;;;;;;;;;;;;;sDAGtD,6LAAC;4CAAI,WAAU;4CAAoD,0BAA0B;sDAC3F,cAAA,6LAAC,sIAAA,CAAA,YAAS;gDAAC,UAAU;;;;;;;;;;;;;;;;;8CAKzB,6LAAC;oCAAI,WAAU;oCAAe,0BAA0B;8CACtD,cAAA,6LAAC,2IAAA,CAAA,iBAAc;wCACb,OAAO;wCACP,gBAAgB;;;;;;;;;;;8CAKpB,6LAAC,sIAAA,CAAA,gBAAa;oCAAC,OAAO;oCAAa,cAAc,cAAc,MAAM;;;;;;;;;;;;sCAIvE,6LAAC;4BAAQ,WAAU;sCAChB,cAAc,KAAK,CAAC,MAAM,KAAK,kBAC9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAkC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACzF,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAClD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,SAAS;4CACP,eAAe;4CACf,eAAe;wCACjB;wCACA,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;gCAA0D,0BAA0B;0CAChG,cAAc,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC,qIAAA,CAAA,WAAQ;wCAEP,MAAM;wCACN,WAAU;wCACV,OAAO;4CACL,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4CACjC,mBAAmB;wCACrB;uCANK,KAAK,EAAE;;;;;;;;;;;;;;;wBAcrB,cAAc,UAAU,GAAG,mBAC1B,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC,mIAAA,CAAA,aAAU;oCACT,aAAa,cAAc,WAAW;oCACtC,YAAY,cAAc,UAAU;oCACpC,cAAc;;;;;;8CAEhB,6LAAC,mIAAA,CAAA,iBAAc;oCACb,aAAa,cAAc,WAAW;oCACtC,YAAY,cAAc,UAAU;oCACpC,YAAY,cAAc,UAAU;oCACpC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAShC;GAxPgB;KAAA", "debugId": null}}, {"offset": {"line": 2295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/skeleton.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface SkeletonProps {\n  className?: string\n}\n\nexport function Skeleton({ className }: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        \"animate-pulse rounded-md bg-muted\",\n        className\n      )}\n    />\n  )\n}\n\nexport function PostCardSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn(\n      \"bg-card rounded-xl shadow-sm border border-border p-6 lg:p-8\",\n      className\n    )}>\n      <div className=\"space-y-4\">\n        {/* Title skeleton */}\n        <Skeleton className=\"h-7 w-3/4\" />\n        \n        {/* Content skeleton - multiple lines */}\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-2/3\" />\n        </div>\n        \n        {/* Footer skeleton */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2\">\n          <div className=\"flex items-center gap-2\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-32\" />\n          </div>\n          <Skeleton className=\"h-4 w-20\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function PostGridSkeleton({ count = 6 }: { count?: number }) {\n  return (\n    <div className=\"grid gap-6 lg:gap-8 md:grid-cols-2 lg:grid-cols-3\">\n      {Array.from({ length: count }).map((_, index) => (\n        <PostCardSkeleton key={index} />\n      ))}\n    </div>\n  )\n}\n\nexport function HeroSkeleton() {\n  return (\n    <div className=\"text-center py-16 lg:py-24 space-y-6\">\n      <div className=\"space-y-4\">\n        <Skeleton className=\"h-12 w-2/3 mx-auto\" />\n        <Skeleton className=\"h-6 w-1/2 mx-auto\" />\n      </div>\n      <div className=\"space-y-3\">\n        <Skeleton className=\"h-4 w-3/4 mx-auto\" />\n        <Skeleton className=\"h-4 w-2/3 mx-auto\" />\n      </div>\n      <Skeleton className=\"h-12 w-40 mx-auto\" />\n    </div>\n  )\n}\n\nexport function SearchBarSkeleton() {\n  return (\n    <div className=\"relative\">\n      <Skeleton className=\"h-12 w-full rounded-lg\" />\n    </div>\n  )\n}\n\nexport function FeaturedPostSkeleton() {\n  return (\n    <div className=\"bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-6 lg:p-8 border border-primary/20\">\n      <div className=\"space-y-4\">\n        {/* Featured badge */}\n        <Skeleton className=\"h-6 w-20\" />\n        \n        {/* Title */}\n        <Skeleton className=\"h-8 w-4/5\" />\n        \n        {/* Content */}\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-3/4\" />\n        </div>\n        \n        {/* Footer */}\n        <div className=\"flex items-center justify-between pt-2\">\n          <div className=\"flex items-center gap-2\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-32\" />\n          </div>\n          <Skeleton className=\"h-4 w-24\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function FilterSkeleton() {\n  return (\n    <div className=\"flex flex-wrap gap-2\">\n      {Array.from({ length: 4 }).map((_, index) => (\n        <Skeleton key={index} className=\"h-8 w-20 rounded-full\" />\n      ))}\n    </div>\n  )\n}\n\nexport function PaginationSkeleton() {\n  return (\n    <div className=\"flex items-center justify-center gap-2\">\n      <Skeleton className=\"h-10 w-20\" />\n      {Array.from({ length: 5 }).map((_, index) => (\n        <Skeleton key={index} className=\"h-10 w-10\" />\n      ))}\n      <Skeleton className=\"h-10 w-20\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAFA;;;AAQO,SAAS,SAAS,EAAE,SAAS,EAAiB;IACnD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA;;;;;;AAIR;KATgB;AAWT,SAAS,iBAAiB,EAAE,SAAS,EAA0B;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gEACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAS,WAAU;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;8BAItB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAS,WAAU;;;;;;8CACpB,6LAAC;oCAAS,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;MA5BgB;AA8BT,SAAS,iBAAiB,EAAE,QAAQ,CAAC,EAAsB;IAChE,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC,sBAAsB;;;;;;;;;;AAI/B;MARgB;AAUT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAEtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAEtB,6LAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B;MAdgB;AAgBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAS,WAAU;;;;;;;;;;;AAG1B;MANgB;AAQT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAS,WAAU;;;;;;8BAGpB,6LAAC;oBAAS,WAAU;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;sCACpB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;8BAItB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAS,WAAU;;;;;;8CACpB,6LAAC;oCAAS,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;MA5BgB;AA8BT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gBAAqB,WAAU;eAAjB;;;;;;;;;;AAIvB;MARgB;AAUT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAS,WAAU;;;;;;YACnB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;oBAAqB,WAAU;mBAAjB;;;;;0BAEjB,6LAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B;MAVgB", "debugId": null}}]}