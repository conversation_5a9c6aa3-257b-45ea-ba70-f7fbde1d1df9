{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/new-post/new-post-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/admin/new-post/new-post-form.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/admin/new-post/new-post-form.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/new-post/new-post-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/admin/new-post/new-post-form.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/admin/new-post/new-post-form.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/new-post/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport NewPostForm from './new-post-form'\n\nexport default async function NewPostPage() {\n  const user = await getCurrentUser()\n  \n  if (!user) {\n    redirect('/login')\n  }\n  \n  const userIsAdmin = await isAdmin(user.id)\n  \n  if (!userIsAdmin) {\n    redirect('/')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n        <div className=\"mb-4 sm:mb-6 lg:mb-8\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mr-4\">\n              <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n              </svg>\n            </div>\n            <div>\n              <h1 className=\"text-2xl sm:text-3xl font-bold text-foreground mb-2\">Create New Post</h1>\n              <p className=\"text-muted-foreground\">Share your thoughts with the world using our markdown editor</p>\n            </div>\n          </div>\n        </div>\n\n        <NewPostForm />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAEhC,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE;IAEzC,IAAI,CAAC,aAAa;QAChB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAK3C,8OAAC,0JAAA,CAAA,UAAW;;;;;;;;;;;;;;;;AAIpB", "debugId": null}}]}