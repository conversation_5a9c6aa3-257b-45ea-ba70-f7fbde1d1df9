{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/markdown-content.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/markdown-content.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/markdown-content.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/markdown-content.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/table-of-contents.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/table-of-contents.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/table-of-contents.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/table-of-contents.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/table-of-contents.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/table-of-contents.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientThemeToggle() from the server but ClientThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/client-theme-toggle.tsx <module evaluation>\",\n    \"ClientThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wEACA", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientThemeToggle() from the server but ClientThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/client-theme-toggle.tsx\",\n    \"ClientThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oDACA", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminActions() from the server but AdminActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin-actions.tsx <module evaluation>\",\n    \"AdminActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,kEACA", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminActions() from the server but AdminActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin-actions.tsx\",\n    \"AdminActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8CACA", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/back-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BackToTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackToTop() from the server but BackToTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/back-to-top.tsx <module evaluation>\",\n    \"BackToTop\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/back-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BackToTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackToTop() from the server but BackToTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/back-to-top.tsx\",\n    \"BackToTop\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4CACA", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-date.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostDate = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostDate() from the server but PostDate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/post-date.tsx <module evaluation>\",\n    \"PostDate\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8DACA", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-date.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostDate = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostDate() from the server but PostDate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/post-date.tsx\",\n    \"PostDate\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0CACA", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/toc.ts"], "sourcesContent": ["export interface TOCItem {\n  id: string\n  title: string\n  level: number\n  children: TOCItem[]\n}\n\n/**\n * Generate a URL-safe anchor ID from heading text\n * Handles special characters, spaces, and non-English text\n */\nexport function generateAnchorId(text: string): string {\n  return text\n    .toLowerCase()\n    .trim()\n    // Replace spaces and special characters with hyphens\n    .replace(/[^\\w\\u4e00-\\u9fff\\u3400-\\u4dbf\\u3040-\\u309f\\u30a0-\\u30ff]/g, '-')\n    // Remove multiple consecutive hyphens\n    .replace(/-+/g, '-')\n    // Remove leading/trailing hyphens\n    .replace(/^-|-$/g, '')\n    // Ensure it's not empty\n    || 'heading'\n}\n\n/**\n * Parse markdown content and extract headings with their levels\n */\nexport function parseMarkdownHeadings(content: string): Array<{ level: number; title: string; id: string }> {\n  const headingRegex = /^(#{1,6})\\s+(.+)$/gm\n  const headings: Array<{ level: number; title: string; id: string }> = []\n  let match\n\n  while ((match = headingRegex.exec(content)) !== null) {\n    const level = match[1].length\n    const title = match[2].trim()\n    const id = generateAnchorId(title)\n    \n    headings.push({ level, title, id })\n  }\n\n  return headings\n}\n\n/**\n * Build a hierarchical table of contents structure\n */\nexport function buildTOCTree(headings: Array<{ level: number; title: string; id: string }>): TOCItem[] {\n  const toc: TOCItem[] = []\n  const stack: TOCItem[] = []\n\n  for (const heading of headings) {\n    const item: TOCItem = {\n      id: heading.id,\n      title: heading.title,\n      level: heading.level,\n      children: []\n    }\n\n    // Find the correct parent level\n    while (stack.length > 0 && stack[stack.length - 1].level >= heading.level) {\n      stack.pop()\n    }\n\n    if (stack.length === 0) {\n      // Top level item\n      toc.push(item)\n    } else {\n      // Child item\n      stack[stack.length - 1].children.push(item)\n    }\n\n    stack.push(item)\n  }\n\n  return toc\n}\n\n/**\n * Generate table of contents from markdown content\n */\nexport function generateTOC(content: string): TOCItem[] {\n  const headings = parseMarkdownHeadings(content)\n  return buildTOCTree(headings)\n}\n\n/**\n * Add anchor IDs to markdown headings\n */\nexport function addAnchorIds(content: string): string {\n  return content.replace(/^(#{1,6})\\s+(.+)$/gm, (match, hashes, title) => {\n    const id = generateAnchorId(title.trim())\n    return `${hashes} ${title.trim()} {#${id}}`\n  })\n}\n\n/**\n * Flatten TOC tree for easier iteration\n */\nexport function flattenTOC(toc: TOCItem[]): TOCItem[] {\n  const flattened: TOCItem[] = []\n  \n  function traverse(items: TOCItem[]) {\n    for (const item of items) {\n      flattened.push(item)\n      if (item.children.length > 0) {\n        traverse(item.children)\n      }\n    }\n  }\n  \n  traverse(toc)\n  return flattened\n}\n"], "names": [], "mappings": ";;;;;;;;AAWO,SAAS,iBAAiB,IAAY;IAC3C,OAAO,KACJ,WAAW,GACX,IAAI,EACL,qDAAqD;KACpD,OAAO,CAAC,8DAA8D,IACvE,sCAAsC;KACrC,OAAO,CAAC,OAAO,IAChB,kCAAkC;KACjC,OAAO,CAAC,UAAU,OAEhB;AACP;AAKO,SAAS,sBAAsB,OAAe;IACnD,MAAM,eAAe;IACrB,MAAM,WAAgE,EAAE;IACxE,IAAI;IAEJ,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,QAAQ,MAAM,KAAM;QACpD,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;QAC7B,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,IAAI;QAC3B,MAAM,KAAK,iBAAiB;QAE5B,SAAS,IAAI,CAAC;YAAE;YAAO;YAAO;QAAG;IACnC;IAEA,OAAO;AACT;AAKO,SAAS,aAAa,QAA6D;IACxF,MAAM,MAAiB,EAAE;IACzB,MAAM,QAAmB,EAAE;IAE3B,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,OAAgB;YACpB,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,UAAU,EAAE;QACd;QAEA,gCAAgC;QAChC,MAAO,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI,QAAQ,KAAK,CAAE;YACzE,MAAM,GAAG;QACX;QAEA,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,iBAAiB;YACjB,IAAI,IAAI,CAAC;QACX,OAAO;YACL,aAAa;YACb,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;QACxC;QAEA,MAAM,IAAI,CAAC;IACb;IAEA,OAAO;AACT;AAKO,SAAS,YAAY,OAAe;IACzC,MAAM,WAAW,sBAAsB;IACvC,OAAO,aAAa;AACtB;AAKO,SAAS,aAAa,OAAe;IAC1C,OAAO,QAAQ,OAAO,CAAC,uBAAuB,CAAC,OAAO,QAAQ;QAC5D,MAAM,KAAK,iBAAiB,MAAM,IAAI;QACtC,OAAO,GAAG,OAAO,CAAC,EAAE,MAAM,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7C;AACF;AAKO,SAAS,WAAW,GAAc;IACvC,MAAM,YAAuB,EAAE;IAE/B,SAAS,SAAS,KAAgB;QAChC,KAAK,MAAM,QAAQ,MAAO;YACxB,UAAU,IAAI,CAAC;YACf,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC5B,SAAS,KAAK,QAAQ;YACxB;QACF;IACF;IAEA,SAAS;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/posts/%5Bid%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport Link from 'next/link'\nimport { createClient } from '@/lib/supabase/server'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { Post } from '@/types/database'\n\nimport MarkdownContent from '@/components/markdown-content'\nimport TableOfContents from '@/components/table-of-contents'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\nimport { AdminActions } from '@/components/admin-actions'\nimport { BackToTop } from '@/components/back-to-top'\nimport { PostDate } from '@/components/post-date'\nimport { generateTOC } from '@/lib/toc'\n\nasync function getPost(id: string): Promise<Post | null> {\n  const supabase = await createClient()\n  const { data: post, error } = await supabase\n    .from('posts')\n    .select('*')\n    .eq('id', id)\n    .single()\n\n  if (error) {\n    console.error('Error fetching post:', error)\n    return null\n  }\n\n  return post\n}\n\nexport default async function PostPage({ params }: { params: Promise<{ id: string }> }) {\n  const { id } = await params\n  const post = await getPost(id)\n  \n  if (!post) {\n    notFound()\n  }\n\n  const user = await getCurrentUser()\n  const userIsAdmin = user ? await isAdmin(user.id) : false\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link\n              href=\"/\"\n              className=\"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors\"\n            >\n              My Blog\n            </Link>\n            <div className=\"flex items-center gap-2 sm:gap-4\">\n              <ClientThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"hidden sm:block text-sm text-muted-foreground\">\n                    Welcome, {user.email}\n                  </span>\n                  {userIsAdmin && (\n                    <>\n                      <Link\n                        href=\"/admin/new-post\"\n                        className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                      >\n                        <span className=\"hidden sm:inline\">New Post</span>\n                        <span className=\"sm:hidden\">+</span>\n                      </Link>\n                      <Link\n                        href=\"/admin/manage-posts\"\n                        className=\"bg-secondary text-secondary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                      >\n                        <span className=\"hidden sm:inline\">Manage</span>\n                        <span className=\"sm:hidden\">⚙️</span>\n                      </Link>\n                    </>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md\"\n                    >\n                      <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      Sign Out\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                >\n                  Sign In\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n        <div className=\"lg:flex lg:gap-8\">\n          {/* Article Content */}\n          <div className=\"lg:flex-1 lg:max-w-4xl\">\n        {/* Navigation Breadcrumb */}\n        <nav className=\"mb-4 sm:mb-6 lg:mb-8\" aria-label=\"Breadcrumb\">\n          <div className=\"flex items-center space-x-2 text-sm\">\n            <Link\n              href=\"/\"\n              className=\"inline-flex items-center text-muted-foreground hover:text-primary transition-all duration-200 group font-medium\"\n            >\n              <svg className=\"w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n              </svg>\n              Home\n            </Link>\n            <svg className=\"w-4 h-4 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n            <span className=\"text-foreground font-medium truncate max-w-xs sm:max-w-md\">\n              {post.title}\n            </span>\n          </div>\n        </nav>\n\n        {/* Article Content */}\n        <article className=\"bg-card rounded-2xl shadow-xl border border-border overflow-hidden animate-fade-in\">\n          {/* Article Header */}\n          <header className=\"relative p-4 sm:p-6 lg:p-8 border-b border-border\">\n            {/* Background Pattern */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5\"></div>\n            <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]\"></div>\n\n            <div className=\"relative\">\n              <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-card-foreground mb-4 sm:mb-6 leading-tight tracking-tight\">\n                {post.title}\n              </h1>\n\n              {/* Article Meta */}\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 text-sm\">\n                <div className=\"flex items-center gap-3 sm:gap-4\">\n                  <PostDate date={post.created_at} />\n\n                  {post.updated_at !== post.created_at && (\n                    <PostDate date={post.updated_at} isUpdateDate={true} />\n                  )}\n                </div>\n\n                {/* Reading Time Estimate */}\n                <div className=\"flex items-center text-muted-foreground bg-muted/50 px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm\">\n                  <svg className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {Math.max(1, Math.ceil(post.content.split(' ').length / 200))} min read\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Article Body */}\n          <div className=\"p-4 sm:p-6 lg:p-8\">\n            <MarkdownContent\n              content={post.content}\n              className=\"prose-headings:text-foreground prose-headings:font-bold prose-headings:tracking-tight\n                        prose-p:text-muted-foreground prose-p:leading-relaxed prose-p:text-base sm:prose-p:text-lg prose-p:mb-2\n                        prose-strong:text-foreground prose-strong:font-semibold\n                        prose-code:text-primary prose-code:bg-muted prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm\n                        prose-pre:bg-muted prose-pre:border prose-pre:border-border prose-pre:rounded-lg prose-pre:p-4\n                        prose-blockquote:border-l-primary prose-blockquote:bg-muted/30 prose-blockquote:pl-6 prose-blockquote:py-2\n                        prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-a:font-medium\n                        prose-ul:text-muted-foreground prose-ol:text-muted-foreground\n                        prose-li:text-muted-foreground prose-li:leading-relaxed\n                        prose-img:rounded-lg prose-img:shadow-md prose-img:border prose-img:border-border\n                        prose-hr:border-border prose-hr:my-8\"\n            />\n          </div>\n        </article>\n\n            {/* Admin Actions */}\n            {userIsAdmin && (\n              <div className=\"mt-6 sm:mt-8 p-4 sm:p-6 bg-gradient-to-r from-muted/50 to-muted/30 rounded-2xl border border-border shadow-lg animate-slide-in\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h3 className=\"text-xl font-bold text-foreground flex items-center\">\n                    <div className=\"w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center mr-3\">\n                      <svg className=\"w-5 h-5 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      </svg>\n                    </div>\n                    Admin Actions\n                  </h3>\n                  <span className=\"text-xs text-muted-foreground bg-muted px-3 py-1 rounded-full\">\n                    Admin Only\n                  </span>\n                </div>\n                <AdminActions postId={post.id} />\n              </div>\n            )}\n          </div>\n\n          {/* Table of Contents Sidebar */}\n          <div className=\"lg:w-80 lg:flex-shrink-0\">\n            <TableOfContents toc={generateTOC(post.content)} />\n          </div>\n        </div>\n\n      </main>\n\n      {/* Floating Back to Top Button */}\n      <BackToTop />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA,eAAe,QAAQ,EAAU;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;IAEA,OAAO;AACT;AAEe,eAAe,SAAS,EAAE,MAAM,EAAuC;IACpF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,OAAO,MAAM,QAAQ;IAE3B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,cAAc,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE,IAAI;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+IAAA,CAAA,oBAAiB;;;;;oCACjB,qBACC;;0DACE,8OAAC;gDAAK,WAAU;;oDAAgD;oDACpD,KAAK,KAAK;;;;;;;4CAErB,6BACC;;kEACE,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;;;;;;kEAE9B,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;qEAMZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEjB,8OAAC;oCAAI,WAAU;oCAAuB,cAAW;8CAC/C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;wDAA+D,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtH,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;oDACjE;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;gDAAgC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACvF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;;;;;;;;;;;;8CAMjB,8OAAC;oCAAQ,WAAU;;sDAEjB,8OAAC;4CAAO,WAAU;;8DAEhB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAIb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,WAAQ;4EAAC,MAAM,KAAK,UAAU;;;;;;wEAE9B,KAAK,UAAU,KAAK,KAAK,UAAU,kBAClC,8OAAC,kIAAA,CAAA,WAAQ;4EAAC,MAAM,KAAK,UAAU;4EAAE,cAAc;;;;;;;;;;;;8EAKnD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAqC,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFAC5F,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;wEAEtE,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,GAAG;wEAAM;;;;;;;;;;;;;;;;;;;;;;;;;sDAOtE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yIAAA,CAAA,UAAe;gDACd,SAAS,KAAK,OAAO;gDACrB,WAAU;;;;;;;;;;;;;;;;;gCAgBX,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAuB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;;kFAC9E,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;kFACrE,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;wDAEnE;;;;;;;8DAGR,8OAAC;oDAAK,WAAU;8DAAgE;;;;;;;;;;;;sDAIlF,8OAAC,sIAAA,CAAA,eAAY;4CAAC,QAAQ,KAAK,EAAE;;;;;;;;;;;;;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6IAAA,CAAA,UAAe;gCAAC,KAAK,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;0BAOpD,8OAAC,uIAAA,CAAA,YAAS;;;;;;;;;;;AAGhB", "debugId": null}}]}