{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "wC6mVzmAcSTFXEAQa4K0/xSyQABPm2lxWxxuURttMKc=", "__NEXT_PREVIEW_MODE_ID": "656154e30eafe66417ae7f5cee92fa3c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e40eeafb53693016ff7c4be98bcb15c0ec7e5219127a4247a934d70c5547a0c3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e19668fe016e29dd1beb3f12ac833f1d134ca20780a9fd1d0b2b50c9b8f8c06f"}}}, "sortedMiddleware": ["/"], "functions": {}}