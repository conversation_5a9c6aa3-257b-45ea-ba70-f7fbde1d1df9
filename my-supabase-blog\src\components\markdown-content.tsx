'use client'

import { useMemo } from 'react'
import { generateAnchorId } from '@/lib/toc'

interface MarkdownContentProps {
  content: string
  className?: string
}

/**
 * Custom markdown renderer that adds anchor IDs to headings
 */
export default function MarkdownContent({ content, className = '' }: MarkdownContentProps) {
  const processedContent = useMemo(() => {
    // Process the markdown content to add anchor IDs to headings
    return content.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
      const level = hashes.length as keyof typeof headingClasses
      const cleanTitle = title.trim()
      const id = generateAnchorId(cleanTitle)
      
      // Return the heading with an anchor ID and proper styling
      const headingClasses = {
        1: 'text-3xl sm:text-4xl font-bold mt-4 mb-3 first:mt-0',
        2: 'text-2xl sm:text-3xl font-bold mt-4 mb-2 first:mt-0',
        3: 'text-xl sm:text-2xl font-semibold mt-3 mb-2 first:mt-0',
        4: 'text-lg sm:text-xl font-semibold mt-3 mb-2 first:mt-0',
        5: 'text-base sm:text-lg font-semibold mt-2 mb-1 first:mt-0',
        6: 'text-sm sm:text-base font-semibold mt-2 mb-1 first:mt-0'
      }

      return `<h${level} id="${id}" class="heading-with-anchor group relative ${headingClasses[level]} text-foreground scroll-mt-20">
        ${cleanTitle}
        <a href="#${id}" class="anchor-link opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-2 text-muted-foreground hover:text-primary no-underline" aria-label="Link to ${cleanTitle}">
          <svg class="w-4 h-4 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
        </a>
      </h${level}>`
    })
  }, [content])

  const renderMarkdown = (text: string) => {
    let html = text

    // Convert headings (already processed above)
    // Convert bold text
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    html = html.replace(/__(.*?)__/g, '<strong>$1</strong>')
    
    // Convert italic text
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
    html = html.replace(/_(.*?)_/g, '<em>$1</em>')
    
    // Convert inline code
    html = html.replace(/`([^`]+)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm font-mono">$1</code>')
    
    // Convert links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary hover:underline">$1</a>')
    
    // Convert code blocks
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
      return `<pre class="bg-muted p-4 rounded-lg overflow-x-auto my-4"><code class="text-sm font-mono">${code.trim()}</code></pre>`
    })
    
    // Convert blockquotes
    html = html.replace(/^>\s+(.+)$/gm, '<blockquote class="border-l-4 border-primary pl-4 italic text-muted-foreground my-4">$1</blockquote>')
    
    // Convert unordered lists
    html = html.replace(/^[\s]*[-*+]\s+(.+)$/gm, '<li class="ml-4">$1</li>')
    html = html.replace(/(<li.*<\/li>)/s, '<ul class="list-disc list-inside space-y-1 my-4">$1</ul>')
    
    // Convert ordered lists
    html = html.replace(/^[\s]*\d+\.\s+(.+)$/gm, '<li class="ml-4">$1</li>')
    html = html.replace(/(<li.*<\/li>)/s, '<ol class="list-decimal list-inside space-y-1 my-4">$1</ol>')
    
    // Ensure paragraphs are correctly formed, handling multiple newlines
    html = html.split('\n\n').map(paragraph => {
      // If a paragraph contains a block-level element, don't wrap it
      if (paragraph.startsWith('<h') || paragraph.startsWith('<ul') || paragraph.startsWith('<ol') || paragraph.startsWith('<blockquote') || paragraph.startsWith('<pre')) {
        return paragraph;
      }
      // Replace single newlines within a paragraph with <br>
      paragraph = paragraph.replace(/\n/g, '<br>');
      return `<p class="mb-4">${paragraph}</p>`;
    }).join('');
    
    return html
  }

  return (
    <div 
      className={`prose prose-base sm:prose-lg max-w-none dark:prose-invert ${className}`}
      dangerouslySetInnerHTML={{ __html: renderMarkdown(processedContent) }}
    />
  )
}
