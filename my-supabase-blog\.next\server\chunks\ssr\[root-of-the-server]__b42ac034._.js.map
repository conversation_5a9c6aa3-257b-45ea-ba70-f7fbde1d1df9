{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-toggle.tsx <module evaluation>\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme-toggle.tsx\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/manage-posts/manage-posts-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/admin/manage-posts/manage-posts-client.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/admin/manage-posts/manage-posts-client.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/manage-posts/manage-posts-client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/admin/manage-posts/manage-posts-client.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/admin/manage-posts/manage-posts-client.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/manage-posts/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport Link from 'next/link'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { createClient } from '@/lib/supabase/server'\nimport { Post } from '@/types/database'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport ManagePostsClient from './manage-posts-client'\n\nasync function getAllPosts(): Promise<Post[]> {\n  const supabase = await createClient()\n  const { data: posts, error } = await supabase\n    .from('posts')\n    .select('*')\n    .order('created_at', { ascending: false })\n\n  if (error) {\n    console.error('Error fetching posts:', error)\n    return []\n  }\n\n  return posts || []\n}\n\nexport default async function ManagePostsPage() {\n  const user = await getCurrentUser()\n  \n  if (!user) {\n    redirect('/login')\n  }\n  \n  const userIsAdmin = await isAdmin(user.id)\n  \n  if (!userIsAdmin) {\n    redirect('/')\n  }\n\n  const posts = await getAllPosts()\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link\n              href=\"/\"\n              className=\"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors\"\n            >\n              My Blog\n            </Link>\n            <div className=\"flex items-center gap-2 sm:gap-4\">\n              <ThemeToggle />\n              <Link\n                href=\"/admin/new-post\"\n                className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n              >\n                <span className=\"hidden sm:inline\">New Post</span>\n                <span className=\"sm:hidden\">+</span>\n              </Link>\n              <form action=\"/auth/signout\" method=\"post\">\n                <button\n                  type=\"submit\"\n                  className=\"inline-flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md\"\n                >\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                  </svg>\n                  Sign Out\n                </button>\n              </form>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12\">\n        <div className=\"mb-8 lg:mb-12\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mr-4\">\n              <svg className=\"w-6 h-6 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n              </svg>\n            </div>\n            <div>\n              <h1 className=\"text-2xl sm:text-3xl font-bold text-foreground mb-2\">Manage Posts</h1>\n              <p className=\"text-muted-foreground\">View, edit, and delete your blog posts</p>\n            </div>\n          </div>\n        </div>\n\n        <ManagePostsClient posts={posts} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,EAAE;AACpB;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAEhC,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE;IAEzC,IAAI,CAAC,aAAa;QAChB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,QAAQ,MAAM;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAA,CAAA,cAAW;;;;;kDACZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAE9B,8OAAC;wCAAK,QAAO;wCAAgB,QAAO;kDAClC,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAK3C,8OAAC,oKAAA,CAAA,UAAiB;wBAAC,OAAO;;;;;;;;;;;;;;;;;;AAIlC", "debugId": null}}]}