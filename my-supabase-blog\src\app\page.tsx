import { Suspense } from 'react'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser, isAdmin } from '@/lib/auth'
import { Post } from '@/types/database'
import { HomePageClient } from '@/components/homepage-client'
import { HeroSkeleton, PostGridSkeleton } from '@/components/skeleton'

async function getPosts(): Promise<Post[]> {
  const supabase = await createClient()
  const { data: posts, error } = await supabase
    .from('posts')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching posts:', error)
    return []
  }

  console.log('Fetched posts count:', posts?.length || 0)
  return posts || []
}

export default async function Home() {
  const posts = await getPosts()
  const user = await getCurrentUser()
  const userIsAdmin = user ? await isAdmin(user.id) : false

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <HeroSkeleton />
          <PostGridSkeleton />
        </div>
      </div>
    }>
      <HomePageClient
        posts={posts}
        user={user}
        userIsAdmin={userIsAdmin}
      />
    </Suspense>
  )
}
