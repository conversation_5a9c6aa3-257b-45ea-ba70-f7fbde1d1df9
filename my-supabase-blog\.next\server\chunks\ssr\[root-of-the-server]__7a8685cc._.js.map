{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport async function getCurrentUser() {\n  const supabase = await createClient()\n  const { data: { user } } = await supabase.auth.getUser()\n  return user\n}\n\nexport async function isAdmin(userId?: string) {\n  if (!userId) return false\n\n  const supabase = await createClient()\n\n  // 方法1: 檢查 profiles 表中的 is_admin 欄位\n  const { data: profile } = await supabase\n    .from('profiles')\n    .select('is_admin')\n    .eq('id', userId)\n    .single()\n\n  if (profile?.is_admin) return true\n\n  // 方法2: 檢查是否為預設管理員 email\n  const { data: { user } } = await supabase.auth.getUser()\n  const adminEmail = process.env.ADMIN_EMAIL\n\n  return user?.email === adminEmail\n}\n\nexport async function requireAdmin() {\n  const user = await getCurrentUser()\n  if (!user) {\n    throw new Error('未登入')\n  }\n\n  const isUserAdmin = await isAdmin(user.id)\n  if (!isUserAdmin) {\n    throw new Error('需要管理員權限')\n  }\n\n  return user\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,OAAO;AACT;AAEO,eAAe,QAAQ,MAAe;IAC3C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,mCAAmC;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,SAAS,UAAU,OAAO;IAE9B,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW;IAE1C,OAAO,MAAM,UAAU;AACzB;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,QAAQ,KAAK,EAAE;IACzC,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientThemeToggle() from the server but ClientThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/client-theme-toggle.tsx <module evaluation>\",\n    \"ClientThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wEACA", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientThemeToggle() from the server but ClientThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/client-theme-toggle.tsx\",\n    \"ClientThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oDACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { createClient } from '@/lib/supabase/server'\nimport { getCurrentUser, isAdmin } from '@/lib/auth'\nimport { Post } from '@/types/database'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\n\nasync function getPosts(): Promise<Post[]> {\n  const supabase = await createClient()\n  const { data: posts, error } = await supabase\n    .from('posts')\n    .select('*')\n    .order('created_at', { ascending: false })\n\n  if (error) {\n    console.error('Error fetching posts:', error)\n    return []\n  }\n\n  return posts || []\n}\n\nexport default async function Home() {\n  const posts = await getPosts()\n  const user = await getCurrentUser()\n  const userIsAdmin = user ? await isAdmin(user.id) : false\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <h1 className=\"text-2xl sm:text-3xl font-bold text-foreground\">My Blog</h1>\n            <div className=\"flex items-center gap-2 sm:gap-4\">\n              <ClientThemeToggle />\n              {user ? (\n                <>\n                  <span className=\"hidden sm:block text-sm text-muted-foreground\">\n                    Welcome, {user.email}\n                  </span>\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/new-post\"\n                      className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                    >\n                      <span className=\"hidden sm:inline\">New Post</span>\n                      <span className=\"sm:hidden\">+</span>\n                    </Link>\n                  )}\n                  {userIsAdmin && (\n                    <Link\n                      href=\"/admin/manage-posts\"\n                      className=\"bg-secondary text-secondary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                    >\n                      <span className=\"hidden sm:inline\">Manage</span>\n                      <span className=\"sm:hidden\">⚙️</span>\n                    </Link>\n                  )}\n                  <form action=\"/auth/signout\" method=\"post\">\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md\"\n                    >\n                      <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      Sign Out\n                    </button>\n                  </form>\n                </>\n              ) : (\n                <Link\n                  href=\"/login\"\n                  className=\"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md\"\n                >\n                  Sign In\n                </Link>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12\">\n        {posts.length === 0 ? (\n          <div className=\"text-center py-16 lg:py-24\">\n            <div className=\"max-w-md mx-auto\">\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center\">\n                <svg className=\"w-8 h-8 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">No posts yet</h2>\n              <p className=\"text-muted-foreground mb-6\">Start sharing your thoughts with the world!</p>\n              {userIsAdmin && (\n                <Link\n                  href=\"/admin/new-post\"\n                  className=\"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\n                >\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                  Create your first post\n                </Link>\n              )}\n            </div>\n          </div>\n        ) : (\n          <div className=\"grid gap-6 lg:gap-8\">\n            {posts.map((post, index) => (\n              <article\n                key={post.id}\n                className=\"bg-card rounded-xl shadow-sm border border-border p-6 lg:p-8 hover-lift animate-fade-in transition-all duration-300 hover:border-primary/20 hover:shadow-lg group\"\n                style={{ animationDelay: `${index * 0.1}s` }}\n              >\n                <div className=\"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4\">\n                  <div className=\"flex-1 min-w-0\">\n                    <h2 className=\"text-xl lg:text-2xl font-semibold text-card-foreground mb-3 group-hover:text-primary transition-colors\">\n                      <Link\n                        href={`/posts/${post.id}`}\n                        className=\"block\"\n                      >\n                        {post.title}\n                      </Link>\n                    </h2>\n                    <p className=\"text-muted-foreground mb-4 line-clamp-3 leading-relaxed\">\n                      {post.content.substring(0, 200)}\n                      {post.content.length > 200 && '...'}\n                    </p>\n                    <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\">\n                      <time className=\"text-sm text-muted-foreground flex items-center\">\n                        <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                        </svg>\n                        {new Date(post.created_at).toLocaleDateString('en-US', {\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })}\n                      </time>\n                      <Link\n                        href={`/posts/${post.id}`}\n                        className=\"text-primary hover:text-primary/80 font-medium transition-all duration-200 group/link inline-flex items-center text-sm\"\n                      >\n                        Read more\n                        <svg className=\"w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              </article>\n            ))}\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,EAAE;AACpB;AAEe,eAAe;IAC5B,MAAM,QAAQ,MAAM;IACpB,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,cAAc,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,EAAE,IAAI;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+IAAA,CAAA,oBAAiB;;;;;oCACjB,qBACC;;0DACE,8OAAC;gDAAK,WAAU;;oDAAgD;oDACpD,KAAK,KAAK;;;;;;;4CAErB,6BACC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,8OAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;4CAG/B,6BACC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,8OAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;0DAGhC,8OAAC;gDAAK,QAAO;gDAAgB,QAAO;0DAClC,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;qEAMZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAK,WAAU;0BACb,MAAM,MAAM,KAAK,kBAChB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAgC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACvF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;4BACzC,6BACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;yCAOd,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4BAAC;sCAE3C,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gDACzB,WAAU;0DAET,KAAK,KAAK;;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;;gDACV,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG;gDAC1B,KAAK,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;sDAEhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;4DACrD,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP;;;;;;;8DAEF,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oDACzB,WAAU;;wDACX;sEAEC,8OAAC;4DAAI,WAAU;4DAAmE,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC1H,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnC1E,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAgD5B", "debugId": null}}]}