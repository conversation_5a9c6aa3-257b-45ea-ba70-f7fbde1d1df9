{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-renderer.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect } from 'react'\nimport '@uiw/react-markdown-preview/markdown.css'\n\n// Dynamically import MDEditor.Markdown to avoid SSR issues\nconst MDPreview = dynamic(\n  () => import('@uiw/react-md-editor').then(mod => ({ default: mod.default.Markdown })),\n  { ssr: false }\n)\n\ninterface MarkdownRendererProps {\n  content: string\n  className?: string\n}\n\nexport function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Show loading state during SSR or fallback to simple rendering\n  if (!mounted) {\n    const renderContent = (text: string) => {\n      return text\n        .split('\\n')\n        .map((line, index) => (\n          <div key={index} className=\"mb-2\">\n            {line || <br />}\n          </div>\n        ))\n    }\n\n    return (\n      <div className={`markdown-renderer ${className}`}>\n        <div className=\"prose prose-lg max-w-none dark:prose-invert text-foreground\">\n          {renderContent(content)}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`markdown-renderer ${className}`} data-color-mode=\"auto\">\n      <MDPreview\n        source={content}\n        style={{\n          whiteSpace: 'pre-wrap',\n          backgroundColor: 'transparent',\n          color: 'inherit'\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;;AAMA,2DAA2D;AAC3D,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAEpB,KAAK;;AAQF,SAAS,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,EAAyB;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,gEAAgE;IAChE,IAAI,CAAC,SAAS;QACZ,MAAM,gBAAgB,CAAC;YACrB,OAAO,KACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,MAAM,sBACV,8OAAC;oBAAgB,WAAU;8BACxB,sBAAQ,8OAAC;;;;;mBADF;;;;;QAIhB;QAEA,qBACE,8OAAC;YAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;sBAC9C,cAAA,8OAAC;gBAAI,WAAU;0BACZ,cAAc;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;QAAE,mBAAgB;kBAChE,cAAA,8OAAC;YACC,QAAQ;YACR,OAAO;gBACL,YAAY;gBACZ,iBAAiB;gBACjB,OAAO;YACT;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useRouter } from 'next/navigation'\r\nimport { createClient } from '@/lib/supabase/client'\r\nimport Link from 'next/link'\r\n\r\nexport function AdminActions({ postId }: { postId: string }) {\r\n  const router = useRouter()\r\n  const supabase = createClient()\r\n\r\n  const handleDelete = async () => {\r\n    if (!confirm('Are you sure you want to delete this post? This action cannot be undone.')) return\r\n\r\n    const { error } = await supabase\r\n      .from('posts')\r\n      .delete()\r\n      .eq('id', postId)\r\n\r\n    if (error) {\r\n      alert('Error deleting post: ' + error.message)\r\n    } else {\r\n      alert('Post deleted successfully!')\r\n      router.push('/')\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row gap-3\">\r\n      <Link\r\n        href={`/admin/edit-post/${postId}`}\r\n        className=\"inline-flex items-center justify-center bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n      >\r\n        <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n        </svg>\r\n        Edit Post\r\n      </Link>\r\n      <button\r\n        className=\"inline-flex items-center justify-center bg-destructive text-destructive-foreground px-4 py-2 rounded-lg hover:bg-destructive/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n        onClick={handleDelete}\r\n      >\r\n        <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n        </svg>\r\n        Delete Post\r\n      </button>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS,aAAa,EAAE,MAAM,EAAsB;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,6EAA6E;QAE1F,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,0BAA0B,MAAM,OAAO;QAC/C,OAAO;YACL,MAAM;YACN,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,CAAC,iBAAiB,EAAE,QAAQ;gBAClC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBACjE;;;;;;;0BAGR,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBACjE;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/back-to-top.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport function BackToTop() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true)\n      } else {\n        setIsVisible(false)\n      }\n    }\n\n    window.addEventListener('scroll', toggleVisibility)\n\n    return () => window.removeEventListener('scroll', toggleVisibility)\n  }, [])\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    })\n  }\n\n  if (!isVisible) {\n    return null\n  }\n\n  return (\n    <button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 inline-flex items-center justify-center w-12 h-12 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all duration-200 group hover:scale-110\"\n      aria-label=\"Back to top\"\n    >\n      <svg className=\"w-5 h-5 group-hover:-translate-y-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n      </svg>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,8OAAC;YAAI,WAAU;YAA0D,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjH,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;;;;;;AAI7E", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-date.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\n\r\ninterface PostDateProps {\r\n  date: string\r\n  isUpdateDate?: boolean\r\n}\r\n\r\nexport function PostDate({ date, isUpdateDate = false }: PostDateProps) {\r\n  const [isMobile, setIsMobile] = useState(false)\r\n\r\n  useEffect(() => {\r\n    const checkWidth = () => {\r\n      setIsMobile(window.innerWidth < 640)\r\n    }\r\n    \r\n    // Initial check\r\n    checkWidth()\r\n    \r\n    // Add resize listener\r\n    window.addEventListener('resize', checkWidth)\r\n    \r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkWidth)\r\n  }, [])\r\n\r\n  return (\r\n    <span className=\"flex items-center text-muted-foreground bg-muted/50 px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm\">\r\n      {isUpdateDate && (\r\n        <>\r\n          <svg className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n          </svg>\r\n          <span className=\"hidden sm:inline\">Updated </span>\r\n        </>\r\n      )}\r\n      {new Date(date).toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: isMobile ? undefined : 'numeric'\r\n      })}\r\n    </span>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,SAAS,EAAE,IAAI,EAAE,eAAe,KAAK,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA,gBAAgB;QAChB;QAEA,sBAAsB;QACtB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAK,WAAU;;YACb,8BACC;;kCACE,8OAAC;wBAAI,WAAU;wBAAqC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC5F,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAK,WAAU;kCAAmB;;;;;;;;YAGtC,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;gBAC1C,OAAO;gBACP,KAAK;gBACL,MAAM,WAAW,YAAY;YAC/B;;;;;;;AAGN", "debugId": null}}]}